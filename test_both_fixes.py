#!/usr/bin/env python3
"""
Test both fixes: Chinese character encoding in archives and embedded image OCR processing
"""

import os
import tempfile
import zipfile
import base64
from email_processor import EmailParser
import email

def create_test_email_with_embedded_images():
    """Create a test email with embedded images for OCR testing"""
    
    # Create a simple test image (1x1 pixel PNG) encoded as base64
    # This is a minimal PNG file that can be used for testing
    test_image_base64 = """iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="""
    
    # Create email content with embedded images
    email_content = f"""From: <EMAIL>
To: <EMAIL>
Subject: Test Email with Embedded Images and Chinese Archive
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/html; charset=utf-8

<html>
<body>
<h1>Test Email with Embedded Images</h1>
<p>This email contains embedded images that should be processed by OCR:</p>

<!-- Base64 embedded image -->
<img src="data:image/png;base64,{test_image_base64}" alt="Test Image 1">

<!-- CID referenced image (will be added as attachment) -->
<img src="cid:test-image-2" alt="Test Image 2">

<p>End of email content.</p>
</body>
</html>

--boundary123
Content-Type: image/png
Content-Disposition: inline
Content-ID: <test-image-2>
Content-Transfer-Encoding: base64

{test_image_base64}

--boundary123
Content-Type: application/zip
Content-Disposition: attachment; filename="20240627╔╧╜╗╦∙╨¡▓Θ║»-测试.zip"
Content-Transfer-Encoding: base64

UEsDBBQAAAAIAEuOWVkAAAAAAAAAAAAAAAAYAAAAMjAyNDA2MjfigJTigJTigJTigJTigJQuZG9jUEsBAhQAFAAAAAgAS45ZWQAAAAAAAAAAAAAAABgAAAAAAAAAAAAgAAAAAAAAADIwMjQwNjI34oCU4oCU4oCU4oCU4oCULmRvY1BLBQYAAAAAAQABAEYAAAA+AAAAAAA=

--boundary123--
"""

    return email_content

def test_chinese_encoding_in_archives():
    """Test Chinese character encoding fix in archive processing"""
    print("=== Testing Chinese Character Encoding in Archives ===")
    
    parser = EmailParser()
    
    # Test the specific garbled filename from user's example
    test_cases = [
        "20240627╔╧╜╗╦∙╨¡▓Θ║»/╨¡▓Θí▓2024í│3261║┼.pdf",
        "╨¡▓Θ║»╓ñ╚»2024╞┌3╘┬26╚╒.docx",
        "测试文档-正常中文.txt",  # Normal Chinese should be preserved
    ]
    
    results = []
    for filename in test_cases:
        fixed = parser._fix_filename_encoding(filename)
        
        # Check improvement
        original_chinese = len([c for c in filename if '\u4e00' <= c <= '\u9fff'])
        fixed_chinese = len([c for c in fixed if '\u4e00' <= c <= '\u9fff'])
        original_garbled = len([c for c in filename if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚»'])
        fixed_garbled = len([c for c in fixed if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚»'])
        
        is_improved = (fixed_chinese > original_chinese) or (fixed_garbled < original_garbled)
        
        result = {
            'original': filename,
            'fixed': fixed,
            'is_improved': is_improved,
            'chinese_chars': f"{original_chinese} -> {fixed_chinese}",
            'garbled_chars': f"{original_garbled} -> {fixed_garbled}"
        }
        results.append(result)
        
        status = "✅ IMPROVED" if is_improved else "➡️ UNCHANGED"
        print(f"{status} {filename}")
        print(f"  Fixed: {fixed}")
        print(f"  Chinese: {result['chinese_chars']}, Garbled: {result['garbled_chars']}")
        print()
    
    return results

def test_embedded_image_ocr():
    """Test embedded image OCR processing"""
    print("=== Testing Embedded Image OCR Processing ===")
    
    parser = EmailParser()
    
    # Create test email with embedded images
    email_content = create_test_email_with_embedded_images()
    
    # Create temporary email file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.eml', delete=False, encoding='utf-8') as temp_email:
        temp_email.write(email_content)
        temp_email_path = temp_email.name
    
    try:
        # Parse the email
        result = parser.parse_eml_file(temp_email_path)
        
        print(f"Email parsing result:")
        print(f"Subject: {result['subject']}")
        print(f"Has HTML: {result['has_html']}")
        print(f"Embedded images count: {result['embedded_images_count']}")
        print(f"Embedded images info: {result['embedded_images_info']}")
        print(f"Embedded image OCR text: {result['embedded_image_ocr_text']}")
        print()
        
        # Test the embedded image extraction directly
        msg = email.message_from_string(email_content)
        embedded_images = parser.extract_embedded_images(msg)
        
        print(f"Direct embedded image extraction:")
        print(f"Found {len(embedded_images)} embedded images:")
        
        for i, img in enumerate(embedded_images, 1):
            print(f"  {i}. {img['filename']} ({img['source']}, {img['content_type']}, {img['size']} bytes)")
        
        # Test OCR processing
        if embedded_images:
            ocr_results = parser.process_embedded_images_ocr(embedded_images)
            print(f"\nOCR processing results:")
            for ocr_result in ocr_results:
                print(f"  {ocr_result['filename']}: '{ocr_result['text']}' (Error: {ocr_result['error']})")
        
        return {
            'embedded_images_count': len(embedded_images),
            'ocr_results': len([r for r in (ocr_results if embedded_images else []) if r['text']]),
            'parsing_success': True,
            'result': result
        }
        
    except Exception as e:
        print(f"❌ Error testing embedded image OCR: {e}")
        return {
            'embedded_images_count': 0,
            'ocr_results': 0,
            'parsing_success': False,
            'error': str(e)
        }
    
    finally:
        # Clean up
        try:
            os.unlink(temp_email_path)
        except:
            pass

def test_complete_integration():
    """Test complete integration of both fixes"""
    print("=== Testing Complete Integration ===")
    
    parser = EmailParser()
    
    # Create email with both Chinese archive and embedded images
    email_content = create_test_email_with_embedded_images()
    
    # Create temporary email file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.eml', delete=False, encoding='utf-8') as temp_email:
        temp_email.write(email_content)
        temp_email_path = temp_email.name
    
    try:
        # Parse the complete email
        result = parser.parse_eml_file(temp_email_path)
        
        print("Complete email processing results:")
        print(f"✅ Subject: {result['subject']}")
        print(f"✅ Body length: {len(result['body'])} characters")
        print(f"✅ Has HTML: {result['has_html']}")
        print(f"✅ Attachments count: {result['attachments_count']}")
        print(f"✅ Embedded images count: {result['embedded_images_count']}")
        
        # Check archive processing (if any archives were attached)
        if result['archive_file_list']:
            print(f"✅ Archive file list: {result['archive_file_list']}")
            
            # Check if Chinese characters are properly displayed
            chinese_chars = len([c for c in result['archive_file_list'] if '\u4e00' <= c <= '\u9fff'])
            garbled_chars = len([c for c in result['archive_file_list'] if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚»'])
            
            if chinese_chars > 0:
                print(f"✅ Chinese encoding: Found {chinese_chars} Chinese characters")
            if garbled_chars > 0:
                print(f"⚠️ Chinese encoding: Still has {garbled_chars} garbled characters")
        
        # Check embedded image OCR
        if result['embedded_image_ocr_text']:
            print(f"✅ Embedded image OCR: {result['embedded_image_ocr_text']}")
        else:
            print(f"ℹ️ Embedded image OCR: No text extracted (may be expected for test images)")
        
        if result['embedded_images_info']:
            print(f"✅ Embedded images info: {result['embedded_images_info']}")
        
        return True, result
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False, None
    
    finally:
        # Clean up
        try:
            os.unlink(temp_email_path)
        except:
            pass

if __name__ == "__main__":
    print("=== Testing Both Email Processing Fixes ===")
    print("1. Chinese character encoding in archive file lists")
    print("2. Embedded image OCR processing")
    print()
    
    # Run all tests
    encoding_results = test_chinese_encoding_in_archives()
    ocr_results = test_embedded_image_ocr()
    integration_success, integration_result = test_complete_integration()
    
    # Summary
    print("\n" + "="*60)
    print("COMPREHENSIVE TEST RESULTS")
    print("="*60)
    
    # Chinese encoding results
    encoding_improved = sum(1 for r in encoding_results if r['is_improved'])
    print(f"✅ Chinese encoding: {encoding_improved}/{len(encoding_results)} cases improved")
    
    # Embedded image OCR results
    if ocr_results['parsing_success']:
        print(f"✅ Embedded image detection: {ocr_results['embedded_images_count']} images found")
        print(f"✅ OCR processing: {ocr_results['ocr_results']} images with text extracted")
    else:
        print(f"❌ Embedded image OCR: Failed - {ocr_results.get('error', 'Unknown error')}")
    
    # Integration test
    print(f"✅ Integration test: {'SUCCESS' if integration_success else 'FAILED'}")
    
    if all([encoding_improved > 0, ocr_results['parsing_success'], integration_success]):
        print("\n🎉 ALL FIXES WORKING CORRECTLY!")
        print("Both issues have been successfully resolved:")
        print("  • Chinese character encoding in archive file lists")
        print("  • Embedded image OCR processing")
    else:
        print("\n⚠️ Some issues may remain - please review the test results above")
    
    print("\n" + "="*60)
