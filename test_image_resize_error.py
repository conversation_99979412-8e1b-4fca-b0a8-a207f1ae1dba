#!/usr/bin/env python3
"""
Test script to reproduce the image resize error
"""

import io
import sys
from PIL import Image
import numpy as np

def create_large_test_image(width=5712, height=4284):
    """Create a large test image to trigger the resize error"""
    print(f"Creating test image with dimensions: {width}x{height}")
    
    # Create a simple test image with some text-like patterns
    image = Image.new('RGB', (width, height), color='white')
    
    # Add some simple patterns to make it look like it has content
    import random
    pixels = image.load()
    for i in range(0, width, 100):
        for j in range(0, height, 100):
            # Add some random rectangles to simulate text
            color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))
            for x in range(i, min(i+50, width)):
                for y in range(j, min(j+20, height)):
                    if x < width and y < height:
                        pixels[x, y] = color
    
    return image

def test_paddleocr_with_large_image():
    """Test PaddleOCR with a large image to reproduce the error"""
    try:
        print("Importing PaddleOCR...")
        from paddleocr import PaddleOCR
        
        print("Initializing PaddleOCR...")
        ocr = PaddleOCR(lang='ch', use_angle_cls=True, use_gpu=False)
        
        print("Creating large test image...")
        large_image = create_large_test_image(5712, 4284)
        
        print("Converting image to numpy array...")
        image_array = np.array(large_image)
        
        print(f"Image array shape: {image_array.shape}")
        print("Running OCR on large image...")
        
        # This should trigger the error
        result = ocr.ocr(image_array)
        
        print("OCR completed successfully!")
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"Error occurred: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

def test_with_paddle_ocr_service():
    """Test using our PaddleOCR service wrapper"""
    try:
        print("\n=== Testing with PaddleOCR Service ===")
        from paddle_ocr_service import PaddleOcrService

        print("Initializing PaddleOCR service...")
        ocr_service = PaddleOcrService()

        print("Creating large test image...")
        large_image = create_large_test_image(5712, 4284)

        # Convert to bytes
        img_byte_arr = io.BytesIO()
        large_image.save(img_byte_arr, format='PNG')
        img_bytes = img_byte_arr.getvalue()

        print(f"Image size in bytes: {len(img_bytes)}")
        print("Running OCR through service...")

        result = ocr_service.extract_text_from_image(img_bytes)

        print("OCR completed successfully!")
        print(f"Extracted text length: {len(result)}")

    except Exception as e:
        print(f"Error occurred: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()

def test_various_image_sizes():
    """Test with various image sizes to identify failure conditions"""
    print("\n=== Testing Various Image Sizes ===")

    test_sizes = [
        (3000, 2000),   # Below limit
        (4000, 3000),   # At limit
        (5000, 3000),   # Width exceeds limit
        (3000, 5000),   # Height exceeds limit
        (5712, 4284),   # Both exceed limit (original error case)
        (8000, 6000),   # Much larger
        (10000, 1000),  # Very wide
        (1000, 10000),  # Very tall
    ]

    from paddle_ocr_service import PaddleOcrService
    ocr_service = PaddleOcrService()

    for width, height in test_sizes:
        print(f"\n--- Testing {width}x{height} ---")
        try:
            # Create test image
            image = create_large_test_image(width, height)

            # Convert to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()

            print(f"Image size: {width}x{height}, bytes: {len(img_bytes)}")

            # Run OCR
            result = ocr_service.extract_text_from_image(img_bytes)

            print(f"✅ SUCCESS - Extracted text length: {len(result)}")

        except Exception as e:
            print(f"❌ FAILED - Error: {e}")
            print(f"Error type: {type(e)}")

def test_memory_intensive_scenario():
    """Test scenario that might cause memory issues"""
    print("\n=== Testing Memory Intensive Scenario ===")

    try:
        from paddle_ocr_service import PaddleOcrService
        ocr_service = PaddleOcrService()

        # Test multiple large images in sequence
        for i in range(3):
            print(f"\n--- Processing large image {i+1}/3 ---")
            large_image = create_large_test_image(6000, 4500)

            img_byte_arr = io.BytesIO()
            large_image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()

            print(f"Processing image {i+1}: {len(img_bytes)} bytes")
            result = ocr_service.extract_text_from_image(img_bytes)
            print(f"✅ Image {i+1} processed successfully, text length: {len(result)}")

            # Clean up
            del large_image, img_byte_arr, img_bytes
            import gc
            gc.collect()

    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== Testing PaddleOCR with Large Images ===")
    print("This script will test PaddleOCR with images larger than 4000 pixels")
    print()

    # Test direct PaddleOCR usage
    test_paddleocr_with_large_image()

    # Test through our service wrapper
    test_with_paddle_ocr_service()

    # Test various image sizes
    test_various_image_sizes()

    # Test memory intensive scenario
    test_memory_intensive_scenario()
