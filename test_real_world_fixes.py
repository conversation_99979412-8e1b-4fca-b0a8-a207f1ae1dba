#!/usr/bin/env python3
"""
Real-world test of the email processing fixes using actual problematic content
"""

import os
import tempfile
from email_processor import EmailParser
import openpyxl

def create_test_email_with_issues():
    """Create a test email with all three types of issues"""
    
    # Simulate problematic email content
    email_content = """From: <EMAIL>
To: <EMAIL>
Subject: Test Email with Multiple Issues
Content-Type: text/html; charset=utf-8

<html>
<head>
<style>
body { 
    line-height: 1.5; 
    font-family: "Microsoft YaHei UI"; 
    color: rgb(0, 0, 0); 
}
blockquote { 
    margin-top: 0px; 
    margin-bottom: 0px; 
    margin-left: 0.5em; 
}
v\\:* {behavior:url(#default#VML);}
o\\:* {behavior:url(#default#VML);}
x\\:* {behavior:url(#default#VML);}
.shape {behavior:url(#default#VML);}
</style>
</head>
<body>
<div class="FoxDiv20250421155517288198" style="font-size: 14px;">
body { font-size: 14px; font-family: "Microsoft YaHei UI"; color: rgb(0, 0, 0); line-height: 1.5; }
<p style="margin-top: 0px; margin-bottom: 0px;">This is a test email with problematic content.</p>
<div>Contains CSS styles and VML declarations that should be cleaned.</div>
</div>
</body>
</html>"""

    return email_content

def test_complete_email_processing():
    """Test the complete email processing pipeline with fixes"""
    print("=== Testing Complete Email Processing Pipeline ===")
    
    try:
        # Create test email content
        email_content = create_test_email_with_issues()
        
        # Create temporary email file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.eml', delete=False, encoding='utf-8') as temp_email:
            temp_email.write(email_content)
            temp_email_path = temp_email.name
        
        # Create temporary output directory
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "test_output.xlsx")
            
            # Process the email using EmailParser
            parser = EmailParser()

            print(f"Processing test email: {temp_email_path}")

            # Read and parse the email
            with open(temp_email_path, 'rb') as f:
                email_data = f.read()

            # Parse the email
            result = parser.parse_eml_file(temp_email_path)

            if result:
                # Create Excel file manually to test the export
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "Email Analysis"

                # Headers
                headers = [
                    'Subject', 'Sender Email', 'Body Content', 'Archive File List',
                    'Date', 'Has HTML', 'Processing Error'
                ]

                for col, header in enumerate(headers, 1):
                    ws.cell(row=1, column=col, value=header)

                # Data (using correct field names from parse_eml_file)
                data = [
                    parser.sanitize_excel_value(result.get('subject', '')),
                    parser.sanitize_excel_value(result.get('from_email', '')),
                    parser.sanitize_excel_value(result.get('body', '')),
                    parser.sanitize_excel_value(result.get('archive_file_list', '')),
                    parser.sanitize_excel_value(result.get('date', '')),
                    result.get('has_html', False),
                    parser.sanitize_excel_value(result.get('error', ''))
                ]

                for col, value in enumerate(data, 1):
                    ws.cell(row=2, column=col, value=value)

                # Save the file
                wb.save(output_path)
                results = [result]  # Simulate results list
            
            if results:
                print("✅ Email processing completed successfully!")
                
                # Check if Excel file was created and is readable
                if os.path.exists(output_path):
                    try:
                        wb = openpyxl.load_workbook(output_path)
                        ws = wb.active
                        
                        print("✅ Excel file created and readable!")
                        
                        # Check the content
                        headers = [ws.cell(row=1, column=col).value for col in range(1, ws.max_column + 1)]
                        print(f"Headers: {len(headers)} columns")
                        
                        if ws.max_row > 1:
                            # Get the body content to check HTML cleaning
                            body_content_col = None
                            for i, header in enumerate(headers):
                                if header == 'Body Content':
                                    body_content_col = i + 1
                                    break
                            
                            if body_content_col:
                                body_content = ws.cell(row=2, column=body_content_col).value
                                print(f"Body content length: {len(str(body_content))}")
                                
                                # Check if CSS/HTML was cleaned
                                has_css = any(pattern in str(body_content).lower() for pattern in [
                                    'font-family:', 'color:', 'margin:', 'line-height:',
                                    'behavior:url', '<div', '<p', '<style'
                                ])
                                
                                if has_css:
                                    print("⚠️ Body content still contains HTML/CSS artifacts")
                                    print(f"Sample: {str(body_content)[:200]}...")
                                else:
                                    print("✅ Body content successfully cleaned of HTML/CSS")
                                    print(f"Clean content: {str(body_content)[:100]}...")
                            
                        return True, output_path
                        
                    except Exception as e:
                        print(f"❌ Error reading Excel file: {e}")
                        return False, None
                else:
                    print("❌ Excel file was not created")
                    return False, None
            else:
                print("❌ Email processing failed")
                return False, None
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False, None
    
    finally:
        # Clean up
        try:
            if 'temp_email_path' in locals():
                os.unlink(temp_email_path)
        except:
            pass

def test_problematic_characters():
    """Test handling of problematic characters that cause Excel corruption"""
    print("\n=== Testing Problematic Characters ===")
    
    parser = EmailParser()
    
    # Test with actual problematic characters that cause XML corruption
    problematic_strings = [
        "Text with null\x00character",
        "Text with\x0Bvertical tab",
        "Text with\x0Cform feed",
        "Formula =SUM(A1:A10) injection",
        "Unicode issues: \u0000\u0001\u0002",
        "Mixed: Hello\x00World\x0B\x0C=FORMULA()",
    ]
    
    all_safe = True
    
    for i, test_string in enumerate(problematic_strings, 1):
        try:
            sanitized = parser.sanitize_excel_value(test_string)
            
            # Check for XML-invalid characters
            has_invalid = False
            for char in sanitized:
                code = ord(char)
                if not (code == 0x09 or code == 0x0A or code == 0x0D or 
                       (0x20 <= code <= 0xD7FF) or (0xE000 <= code <= 0xFFFD) or 
                       (0x10000 <= code <= 0x10FFFF)):
                    has_invalid = True
                    break
            
            # Check for formula injection
            has_formula = sanitized and sanitized[0] in ['=', '+', '-', '@'] and not sanitized.startswith("'")
            
            is_safe = not has_invalid and not has_formula
            all_safe = all_safe and is_safe
            
            status = "✅ SAFE" if is_safe else "❌ UNSAFE"
            print(f"{status} Test {i}: '{test_string[:30]}...' -> '{sanitized[:30]}...'")
            
            if has_invalid:
                print(f"  ⚠️ Contains XML-invalid characters")
            if has_formula:
                print(f"  ⚠️ Formula injection not prevented")
                
        except Exception as e:
            print(f"❌ Test {i} failed: {e}")
            all_safe = False
    
    return all_safe

def test_html_css_removal():
    """Test comprehensive HTML/CSS removal"""
    print("\n=== Testing HTML/CSS Removal ===")
    
    parser = EmailParser()
    
    # Real-world problematic HTML/CSS content
    html_samples = [
        """body { line-height: 1.5; }blockquote { margin-top: 0px; margin-bottom: 0px; margin-left: 0.5em; }body { font-size: 14px; font-family: "Microsoft YaHei UI"; color: rgb(0, 0, 0); line-height: 1.5; }""",
        
        """v\\:* {behavior:url(#default#VML);} o\\:* {behavior:url(#default#VML);} x\\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);}""",
        
        """<div class="FoxDiv20250421155517288198">body { font-size: 14px; font-family: "Microsoft YaHei UI"; color: rgb(0, 0, 0); line-height: 1.5; }<p style="margin: 0;">Hello World</p></div>""",
        
        """<style>body{margin:0;padding:0;font-family:Arial}</style><div style="color:red;font-size:14px">Content</div>""",
    ]
    
    all_clean = True
    
    for i, html_content in enumerate(html_samples, 1):
        try:
            cleaned = parser._clean_html_content(html_content)
            
            # Check if CSS/HTML artifacts remain
            has_artifacts = any(pattern in cleaned.lower() for pattern in [
                'font-family:', 'color:', 'margin:', 'padding:', 'line-height:',
                'behavior:url', '{', '}', '<', '>', 'style='
            ])
            
            is_clean = not has_artifacts
            all_clean = all_clean and is_clean
            
            status = "✅ CLEAN" if is_clean else "❌ HAS ARTIFACTS"
            print(f"{status} Test {i}: {len(html_content)} -> {len(cleaned)} chars")
            print(f"  Original: {html_content[:50]}...")
            print(f"  Cleaned:  {cleaned[:50]}...")
            
            if has_artifacts:
                print(f"  ⚠️ Still contains CSS/HTML artifacts")
                
        except Exception as e:
            print(f"❌ Test {i} failed: {e}")
            all_clean = False
    
    return all_clean

if __name__ == "__main__":
    print("=== Real-World Testing of Email Processing Fixes ===")
    print("Testing with actual problematic content patterns")
    print()
    
    # Run comprehensive tests
    pipeline_success, excel_path = test_complete_email_processing()
    characters_safe = test_problematic_characters()
    html_clean = test_html_css_removal()
    
    # Final summary
    print("\n" + "="*60)
    print("COMPREHENSIVE TEST RESULTS")
    print("="*60)
    
    print(f"✅ Complete pipeline: {'SUCCESS' if pipeline_success else 'FAILED'}")
    print(f"✅ Character safety: {'PASSED' if characters_safe else 'FAILED'}")
    print(f"✅ HTML/CSS cleaning: {'PASSED' if html_clean else 'FAILED'}")
    
    if excel_path:
        print(f"📁 Test Excel file: {excel_path}")
    
    if all([pipeline_success, characters_safe, html_clean]):
        print("\n🎉 ALL REAL-WORLD TESTS PASSED!")
        print("The fixes are working correctly for:")
        print("  • Excel file corruption prevention")
        print("  • HTML/CSS content cleaning")
        print("  • Character encoding issues")
    else:
        print("\n⚠️ Some tests failed - please review the results above")
    
    print("\n" + "="*60)
