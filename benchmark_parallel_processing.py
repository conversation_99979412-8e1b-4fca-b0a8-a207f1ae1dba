#!/usr/bin/env python3
"""
Benchmark script for parallel processing performance
Compares single-threaded vs multi-threaded email processing
"""

import sys
import os
import time
import tempfile
import shutil
from pathlib import Path
import statistics

def create_benchmark_emails(count=20):
    """Create benchmark email files"""
    print(f"Creating {count} benchmark email files...")
    
    import email
    from email.mime.multipart import MIMEMultipart
    from email.mime.text import MIMEText
    from email.mime.application import MIMEApplication
    
    os.makedirs("benchmark_emails", exist_ok=True)
    
    files = []
    for i in range(count):
        msg = MIMEMultipart()
        msg['Subject'] = f'Benchmark Email {i+1}'
        msg['From'] = f'benchmark{i+1}@test.com'
        msg['To'] = f'target{i+1}@test.com'
        msg['Date'] = email.utils.formatdate(localtime=True)
        
        # Add substantial text content
        text_content = f"""
        Benchmark Email {i+1}
        
        This is a comprehensive benchmark email designed to test the performance
        of both single-threaded and multi-threaded email processing systems.
        
        Content includes:
        - Multiple paragraphs of text
        - Various formatting elements
        - Metadata processing
        - Attachment handling
        
        Performance metrics to measure:
        - Processing time per email
        - Memory usage
        - CPU utilization
        - Thread safety
        
        Test data: {'X' * (200 + i * 20)}
        
        Additional content for processing complexity:
        """ + "\n".join([f"Line {j}: Content for processing test" for j in range(10)])
        
        msg.attach(MIMEText(text_content, 'plain'))
        
        # Add HTML content
        html_content = f"""
        <html>
        <head><title>Benchmark Email {i+1}</title></head>
        <body>
        <h1>Benchmark Email {i+1}</h1>
        <p>This is <strong>HTML content</strong> for benchmark testing.</p>
        <table>
        <tr><th>Metric</th><th>Value</th></tr>
        <tr><td>Email ID</td><td>{i+1}</td></tr>
        <tr><td>Processing Type</td><td>Benchmark</td></tr>
        </table>
        </body>
        </html>
        """
        msg.attach(MIMEText(html_content, 'html'))
        
        # Add attachment for every 3rd email
        if i % 3 == 0:
            attachment_data = f"Attachment data for email {i+1}\n" * 50
            attachment = MIMEApplication(attachment_data.encode())
            attachment.add_header('Content-Disposition', 'attachment', 
                                filename=f'benchmark_attachment_{i+1}.txt')
            msg.attach(attachment)
        
        # Save file
        filename = f"benchmark_email_{i+1:03d}.eml"
        filepath = os.path.join("benchmark_emails", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(msg.as_string())
        
        files.append(filepath)
    
    print(f"✅ Created {len(files)} benchmark files")
    return files

def run_single_threaded_benchmark(files):
    """Run single-threaded processing benchmark"""
    print("\n--- Single-Threaded Benchmark ---")
    
    from email_processor import ProcessingThread
    
    output_file = tempfile.mktemp(suffix='.xlsx')
    
    start_time = time.time()
    
    # Create and run processing thread
    thread = ProcessingThread(files, output_file)
    
    # Capture processing events
    processed_files = []
    
    def on_file_processed(file_path, result):
        processed_files.append((file_path, time.time()))
    
    thread.file_processed.connect(on_file_processed)
    
    # Run the thread
    thread.run()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Calculate metrics
    files_per_second = len(files) / total_time if total_time > 0 else 0
    avg_time_per_file = total_time / len(files) if len(files) > 0 else 0
    
    print(f"  Total time: {total_time:.2f} seconds")
    print(f"  Files per second: {files_per_second:.2f}")
    print(f"  Average time per file: {avg_time_per_file:.3f} seconds")
    
    # Cleanup
    try:
        os.unlink(output_file)
    except:
        pass
    
    return {
        'total_time': total_time,
        'files_per_second': files_per_second,
        'avg_time_per_file': avg_time_per_file,
        'processed_count': len(processed_files)
    }

def run_parallel_benchmark(files, max_workers=4):
    """Run parallel processing benchmark"""
    print(f"\n--- Parallel Benchmark ({max_workers} threads) ---")
    
    from email_processor import ParallelProcessingThread
    
    output_file = tempfile.mktemp(suffix='.xlsx')
    
    start_time = time.time()
    
    # Create and run parallel processing thread
    thread = ParallelProcessingThread(files, output_file, max_workers)
    
    # Capture processing events
    processed_files = []
    
    def on_file_processed(file_path, result):
        processed_files.append((file_path, time.time()))
    
    thread.file_processed.connect(on_file_processed)
    
    # Run the thread
    thread.run()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Calculate metrics
    files_per_second = len(files) / total_time if total_time > 0 else 0
    avg_time_per_file = total_time / len(files) if len(files) > 0 else 0
    
    print(f"  Total time: {total_time:.2f} seconds")
    print(f"  Files per second: {files_per_second:.2f}")
    print(f"  Average time per file: {avg_time_per_file:.3f} seconds")
    
    # Cleanup
    try:
        os.unlink(output_file)
    except:
        pass
    
    return {
        'total_time': total_time,
        'files_per_second': files_per_second,
        'avg_time_per_file': avg_time_per_file,
        'processed_count': len(processed_files)
    }

def run_comprehensive_benchmark():
    """Run comprehensive benchmark with multiple configurations"""
    print("=== Comprehensive Parallel Processing Benchmark ===")
    
    # Test configurations
    file_counts = [5, 10, 20]
    thread_counts = [1, 2, 4, 6]
    
    results = {}
    
    for file_count in file_counts:
        print(f"\n🔄 Testing with {file_count} files...")
        
        # Create test files
        files = create_benchmark_emails(file_count)
        
        results[file_count] = {}
        
        # Test single-threaded
        single_result = run_single_threaded_benchmark(files)
        results[file_count]['single'] = single_result
        
        # Test different thread counts
        for thread_count in thread_counts:
            if thread_count == 1:
                continue  # Skip, already tested single-threaded
            
            parallel_result = run_parallel_benchmark(files, thread_count)
            results[file_count][f'parallel_{thread_count}'] = parallel_result
        
        # Cleanup test files
        shutil.rmtree("benchmark_emails", ignore_errors=True)
    
    # Generate report
    print("\n" + "="*60)
    print("BENCHMARK RESULTS SUMMARY")
    print("="*60)
    
    for file_count in file_counts:
        print(f"\n📊 {file_count} Files:")
        
        single_time = results[file_count]['single']['total_time']
        single_fps = results[file_count]['single']['files_per_second']
        
        print(f"  Single-threaded: {single_time:.2f}s ({single_fps:.2f} files/sec)")
        
        best_speedup = 0
        best_config = None
        
        for thread_count in thread_counts:
            if thread_count == 1:
                continue
            
            key = f'parallel_{thread_count}'
            if key in results[file_count]:
                parallel_time = results[file_count][key]['total_time']
                parallel_fps = results[file_count][key]['files_per_second']
                speedup = single_time / parallel_time if parallel_time > 0 else 0
                
                print(f"  {thread_count} threads: {parallel_time:.2f}s ({parallel_fps:.2f} files/sec) - {speedup:.2f}x speedup")
                
                if speedup > best_speedup:
                    best_speedup = speedup
                    best_config = thread_count
        
        if best_config:
            print(f"  🏆 Best: {best_config} threads with {best_speedup:.2f}x speedup")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"  • For small batches (< 10 files): Single-threaded may be sufficient")
    print(f"  • For medium batches (10-20 files): 2-4 threads recommended")
    print(f"  • For large batches (> 20 files): 4-6 threads optimal")
    print(f"  • Thread count should not exceed CPU cores for best performance")
    
    return results

def main():
    """Run the benchmark"""
    print("Email Processor Parallel Processing Benchmark")
    print("=" * 50)
    
    try:
        # Check if required modules are available
        from email_processor import ProcessingThread, ParallelProcessingThread
        print("✅ Email processor modules loaded successfully")
        
        # Run comprehensive benchmark
        results = run_comprehensive_benchmark()
        
        print("\n🎉 Benchmark completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Required modules not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
