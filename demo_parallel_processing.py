#!/usr/bin/env python3
"""
Demonstration script for parallel processing functionality
Creates sample emails and shows performance comparison
"""

import sys
import os
import time
import tempfile
import shutil
from pathlib import Path

def create_demo_emails(count=6):
    """Create demo email files for testing"""
    print(f"Creating {count} demo email files...")
    
    import email
    from email.mime.multipart import MIMEMultipart
    from email.mime.text import MIMEText
    
    demo_dir = "demo_emails"
    os.makedirs(demo_dir, exist_ok=True)
    
    files = []
    for i in range(count):
        msg = MIMEMultipart()
        msg['Subject'] = f'Demo Email {i+1} - Parallel Processing Test'
        msg['From'] = f'demo{i+1}@example.com'
        msg['To'] = f'user{i+1}@example.com'
        msg['Date'] = email.utils.formatdate(localtime=True)
        
        # Add content
        text_content = f"""
        Demo Email {i+1}
        
        This email demonstrates the parallel processing capabilities of the
        Email Batch Processor application.
        
        Features being tested:
        - Multi-threaded email processing
        - Thread-safe OCR operations
        - Performance monitoring
        - Error handling
        
        Content: {'Demo content ' * (10 + i)}
        """
        
        msg.attach(MIMEText(text_content, 'plain'))
        
        # Save file
        filename = f"demo_email_{i+1:02d}.eml"
        filepath = os.path.join(demo_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(msg.as_string())
        
        files.append(filepath)
    
    print(f"✅ Created {len(files)} demo files in {demo_dir}/")
    return files

def demo_single_threaded(files):
    """Demonstrate single-threaded processing"""
    print("\n=== Single-Threaded Processing Demo ===")
    
    from email_processor import ProcessingThread
    
    output_file = tempfile.mktemp(suffix='.xlsx')
    
    print(f"Processing {len(files)} files with single thread...")
    start_time = time.time()
    
    # Create processing thread
    thread = ProcessingThread(files, output_file)
    
    # Track progress
    processed_count = 0
    def on_progress(progress):
        nonlocal processed_count
        if progress > processed_count:
            processed_count = progress
            print(f"  Progress: {progress}%")
    
    def on_file_processed(file_path, result):
        filename = os.path.basename(file_path)
        if result.get('error'):
            print(f"  ❌ {filename}: {result['error']}")
        else:
            print(f"  ✅ {filename}: Processed successfully")
    
    # Connect signals
    thread.progress_updated.connect(on_progress)
    thread.file_processed.connect(on_file_processed)
    
    # Run processing
    thread.run()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n📊 Single-threaded Results:")
    print(f"  Total time: {total_time:.2f} seconds")
    print(f"  Files per second: {len(files) / total_time:.2f}")
    print(f"  Average time per file: {total_time / len(files):.2f} seconds")
    
    # Cleanup
    try:
        os.unlink(output_file)
    except:
        pass
    
    return total_time

def demo_parallel_processing(files, max_workers=4):
    """Demonstrate parallel processing"""
    print(f"\n=== Parallel Processing Demo ({max_workers} threads) ===")
    
    from email_processor import ParallelProcessingThread
    
    output_file = tempfile.mktemp(suffix='.xlsx')
    
    print(f"Processing {len(files)} files with {max_workers} threads...")
    start_time = time.time()
    
    # Create parallel processing thread
    thread = ParallelProcessingThread(files, output_file, max_workers)
    
    # Track progress
    processed_count = 0
    def on_progress(progress):
        nonlocal processed_count
        if progress > processed_count:
            processed_count = progress
            print(f"  Progress: {progress}%")
    
    def on_file_processed(file_path, result):
        filename = os.path.basename(file_path)
        if result.get('error'):
            print(f"  ❌ {filename}: {result['error']}")
        else:
            print(f"  ✅ {filename}: Processed successfully")
    
    def on_performance_update(perf_text):
        print(f"  📊 {perf_text}")
    
    # Connect signals
    thread.progress_updated.connect(on_progress)
    thread.file_processed.connect(on_file_processed)
    thread.performance_update.connect(on_performance_update)
    
    # Run processing
    thread.run()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n📊 Parallel Processing Results:")
    print(f"  Total time: {total_time:.2f} seconds")
    print(f"  Files per second: {len(files) / total_time:.2f}")
    print(f"  Average time per file: {total_time / len(files):.2f} seconds")
    
    # Cleanup
    try:
        os.unlink(output_file)
    except:
        pass
    
    return total_time

def demo_gui_features():
    """Demonstrate GUI features"""
    print("\n=== GUI Features Demo ===")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from email_processor import EmailProcessorGUI
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create GUI
        gui = EmailProcessorGUI()
        
        print("✅ GUI created successfully")
        print(f"  Threading enabled: {gui.threading_enabled.isChecked()}")
        print(f"  Default thread count: {gui.thread_count_spinbox.value()}")
        print(f"  Thread count range: {gui.thread_count_spinbox.minimum()}-{gui.thread_count_spinbox.maximum()}")
        
        # Test toggle
        print("\nTesting control visibility...")
        gui.threading_enabled.setChecked(False)
        gui.on_threading_toggled(False)
        print(f"  Thread controls hidden when disabled: {not gui.thread_count_spinbox.isVisible()}")
        
        gui.threading_enabled.setChecked(True)
        gui.on_threading_toggled(True)
        print(f"  Thread controls shown when enabled: {gui.thread_count_spinbox.isVisible()}")
        
        print("✅ GUI features working correctly")
        return True
        
    except Exception as e:
        print(f"❌ GUI demo failed: {e}")
        return False

def main():
    """Run the complete demonstration"""
    print("🚀 Email Processor Parallel Processing Demo")
    print("=" * 50)
    
    try:
        # Create demo files
        demo_files = create_demo_emails(6)
        
        # Demo GUI features
        gui_success = demo_gui_features()
        
        # Demo single-threaded processing
        single_time = demo_single_threaded(demo_files)
        
        # Demo parallel processing
        parallel_time = demo_parallel_processing(demo_files, max_workers=3)
        
        # Calculate and display speedup
        if parallel_time > 0:
            speedup = single_time / parallel_time
            print(f"\n🏆 PERFORMANCE COMPARISON:")
            print(f"  Single-threaded: {single_time:.2f}s")
            print(f"  Parallel (3 threads): {parallel_time:.2f}s")
            print(f"  Speedup: {speedup:.2f}x")
            
            if speedup > 1.2:
                print(f"  🎉 Parallel processing is {speedup:.1f}x faster!")
            else:
                print(f"  ⚠️ Speedup is minimal (overhead may dominate for small batches)")
        
        # Cleanup
        shutil.rmtree("demo_emails", ignore_errors=True)
        
        print(f"\n✅ Demo completed successfully!")
        print(f"\nParallel processing features:")
        print(f"  • Thread-safe OCR processing")
        print(f"  • Real-time performance monitoring")
        print(f"  • Intelligent thread management")
        print(f"  • User-friendly GUI controls")
        print(f"  • Robust error handling")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nDemo {'completed successfully' if success else 'failed'}!")
    sys.exit(0 if success else 1)
