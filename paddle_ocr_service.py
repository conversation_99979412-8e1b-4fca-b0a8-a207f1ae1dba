"""
PaddleOCR-based OCR Service for Email Batch Processor
Provides robust OCR functionality with English and Chinese support
"""

import io
import logging
import numpy as np
from typing import List, Optional
from dataclasses import dataclass

# Configure logging to reduce PaddleOCR verbosity
logging.getLogger('ppocr').setLevel(logging.WARNING)
logging.getLogger('paddle').setLevel(logging.WARNING)

# Optional imports (performed lazily)
try:
    from PIL import Image
    _PIL_AVAILABLE = True
except ImportError:
    _PIL_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    _PYMUPDF_AVAILABLE = True
except ImportError:
    _PYMUPDF_AVAILABLE = False


@dataclass
class OcrResult:
    """OCR result container"""
    text: str
    confidence: float = 0.0
    success: bool = True
    error: Optional[str] = None


class PaddleOcrService:
    """
    PaddleOCR-based OCR service with English and Chinese support
    Provides a clean interface for image and PDF text extraction
    """
    
    def __init__(self):
        self._ocr = None
        self._init_error: Optional[str] = None
        self._status = "initializing"
        self._initialize_ocr()
    
    def _initialize_ocr(self):
        """Initialize PaddleOCR with error handling"""
        try:
            # Import PaddleOCR
            from paddleocr import PaddleOCR

            # Check if we can import successfully
            self._paddle_ocr_class = PaddleOCR
            self._ocr = None  # Will be initialized on first use
            self._status = "available:paddleocr"

        except ImportError as e:
            self._init_error = f"PaddleOCR not installed: {str(e)}"
            self._status = "unavailable:missing_paddleocr"

        except Exception as e:
            self._init_error = f"PaddleOCR import failed: {str(e)}"
            self._status = "unavailable:import_failed"

    def _ensure_ocr_initialized(self):
        """Ensure OCR is initialized (lazy initialization)"""
        if self._ocr is None and hasattr(self, '_paddle_ocr_class'):
            try:
                # Initialize PaddleOCR with PP-OCRv5 models for best performance
                # Using Chinese+English support for maximum compatibility
                # Simplified initialization to avoid parameter conflicts
                self._ocr = self._paddle_ocr_class(lang='ch')
                print("✅ PaddleOCR initialized successfully with PP-OCRv5 models")
            except Exception as e:
                # If initialization fails, try fallback configurations
                error_msg = str(e)
                if "already been initialized" in error_msg or "Unknown argument" in error_msg:
                    # Try simpler initialization
                    try:
                        self._ocr = self._paddle_ocr_class(lang='ch')
                        print("✅ PaddleOCR initialized with fallback configuration")
                    except Exception as fallback_error:
                        self._init_error = f"PaddleOCR initialization failed: {fallback_error}"
                        self._status = "unavailable:init_failed"
                        raise RuntimeError(self._init_error)
                else:
                    self._init_error = f"PaddleOCR initialization failed: {error_msg}"
                    self._status = "unavailable:init_failed"
                    raise RuntimeError(self._init_error)
    
    @property
    def status(self) -> str:
        """Get current OCR service status"""
        return self._status
    
    def is_available(self) -> bool:
        """Check if OCR service is available"""
        return (self._ocr is not None or hasattr(self, '_paddle_ocr_class')) and _PIL_AVAILABLE
    
    def _normalize_paddle_result(self, result) -> OcrResult:
        """
        Normalize PaddleOCR result format
        Handles both legacy and modern PaddleOCR v3.2.0+ formats
        """
        if not result:
            return OcrResult(text="", confidence=0.0)

        texts = []
        confidences = []

        try:
            # Handle modern PaddleOCR v3.2.0+ format (dictionary with rec_texts and rec_scores)
            if isinstance(result, list) and len(result) > 0:
                first_item = result[0]

                # Modern format: [{'rec_texts': [...], 'rec_scores': [...], ...}]
                if isinstance(first_item, dict) and 'rec_texts' in first_item and 'rec_scores' in first_item:
                    rec_texts = first_item['rec_texts']
                    rec_scores = first_item['rec_scores']

                    for i, text in enumerate(rec_texts):
                        if text and text.strip():
                            texts.append(text.strip())
                            if i < len(rec_scores):
                                confidences.append(float(rec_scores[i]))
                            else:
                                confidences.append(0.0)

                # Legacy format: [[[bbox], (text, confidence)], ...]
                elif isinstance(first_item, list):
                    # If result is nested (common in older versions)
                    if len(first_item) > 0 and isinstance(first_item[0], list):
                        result = first_item  # Unwrap one level

                    # Process each detected text line
                    for line in result:
                        if isinstance(line, list) and len(line) >= 2:
                            # Extract text and confidence: [bbox, (text, confidence)]
                            text_info = line[1]
                            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                text, confidence = text_info[0], text_info[1]
                                if text and text.strip():
                                    texts.append(text.strip())
                                    confidences.append(float(confidence))
                        elif isinstance(line, dict):
                            # Handle dictionary format (alternative format)
                            if 'text' in line and 'confidence' in line:
                                text = line['text'].strip()
                                if text:
                                    texts.append(text)
                                    confidences.append(float(line['confidence']))

            # Combine all text with proper spacing
            combined_text = " ".join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0

            return OcrResult(
                text=combined_text,
                confidence=avg_confidence,
                success=True
            )

        except Exception as e:
            return OcrResult(
                text="",
                confidence=0.0,
                success=False,
                error=f"Result parsing error: {str(e)}"
            )
    
    def _preprocess_image(self, image: Image.Image, max_side_limit: int = 4000) -> Image.Image:
        """
        Preprocess image to ensure it meets size requirements and is optimized for OCR

        Args:
            image: PIL Image object
            max_side_limit: Maximum allowed dimension for either width or height

        Returns:
            Preprocessed PIL Image object
        """
        width, height = image.size

        # Check if image exceeds the maximum side limit
        if max(width, height) > max_side_limit:
            print(f"Image size ({width}x{height}) exceeds max_side_limit of {max_side_limit}. Preprocessing to fit within limit.")

            # Calculate the scaling ratio to fit within the limit while preserving aspect ratio
            ratio = float(max_side_limit) / max(width, height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)

            # Ensure dimensions are at least 32 pixels (minimum for OCR models)
            new_width = max(new_width, 32)
            new_height = max(new_height, 32)

            print(f"Resizing image from {width}x{height} to {new_width}x{new_height} (ratio: {ratio:.3f})")

            # Use high-quality resampling for better OCR results
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        return image

    def extract_text_from_image(self, image_bytes: bytes) -> str:
        """
        Extract text from image bytes with robust preprocessing

        Args:
            image_bytes: Raw image data

        Returns:
            Extracted text string

        Raises:
            RuntimeError: If OCR service is not available or processing fails
        """
        if not self.is_available():
            raise RuntimeError(f"OCR service not available: {self._init_error or 'Unknown error'}")

        if not _PIL_AVAILABLE:
            raise RuntimeError("PIL (Pillow) is required for image processing")

        try:
            # Ensure OCR is initialized
            self._ensure_ocr_initialized()

            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_bytes))

            # Log original image dimensions
            original_width, original_height = image.size
            print(f"Processing image: {original_width}x{original_height} pixels")

            # Convert to RGB if necessary (PaddleOCR works best with RGB)
            if image.mode != 'RGB':
                image = image.convert('RGB')
                print(f"Converted image from {image.mode} to RGB mode")

            # Preprocess image to handle size constraints
            image = self._preprocess_image(image, max_side_limit=4000)

            # Convert PIL Image to numpy array (required by PaddleOCR v3.2.0+)
            image_array = np.array(image)

            print(f"Image array shape: {image_array.shape}")

            # Perform OCR using the modern predict API
            # The predict method is the recommended approach in PaddleOCR v3.2.0+
            try:
                print("Running OCR using predict API...")
                result = self._ocr.predict(image_array)
            except AttributeError:
                # Fallback to ocr method if predict is not available
                print("Predict API not available, falling back to legacy OCR method...")
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore", DeprecationWarning)
                    result = self._ocr.ocr(image_array)
            except Exception as api_error:
                # If predict fails, try the legacy ocr method
                print(f"Predict API failed ({api_error}), trying legacy OCR method...")
                try:
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore", DeprecationWarning)
                        result = self._ocr.ocr(image_array)
                except Exception as legacy_error:
                    raise RuntimeError(f"Both OCR APIs failed - Predict: {api_error}, Legacy: {legacy_error}")

            # Normalize result
            ocr_result = self._normalize_paddle_result(result)

            if not ocr_result.success:
                raise RuntimeError(ocr_result.error or "OCR processing failed")

            print(f"OCR completed successfully. Extracted text length: {len(ocr_result.text)}")
            return ocr_result.text

        except Exception as e:
            error_msg = str(e)
            print(f"OCR processing error: {error_msg}")

            # Provide more informative error messages
            if "unexpected keyword argument" in error_msg:
                raise RuntimeError(f"PaddleOCR API compatibility issue: {error_msg}")
            elif "not supported input data type" in error_msg:
                raise RuntimeError(f"Image format not supported by PaddleOCR: {error_msg}")
            elif "max_side_limit" in error_msg.lower():
                raise RuntimeError(f"Image size constraint error: {error_msg}")
            else:
                raise RuntimeError(f"Image OCR failed: {error_msg}")
    
    def extract_text_from_pdf(self, pdf_bytes: bytes) -> str:
        """
        Extract text from PDF bytes using OCR
        
        Args:
            pdf_bytes: Raw PDF data
            
        Returns:
            Extracted text string
            
        Raises:
            RuntimeError: If OCR service is not available or processing fails
        """
        if not self.is_available():
            raise RuntimeError(f"OCR service not available: {self._init_error or 'Unknown error'}")
        
        if not _PYMUPDF_AVAILABLE:
            raise RuntimeError("PyMuPDF is required for PDF processing")
        
        doc = None
        texts = []
        
        try:
            # Open PDF document
            doc = fitz.open(stream=pdf_bytes, filetype='pdf')
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # First try to extract native text
                native_text = page.get_text().strip()
                if native_text:
                    texts.append(native_text)
                    continue
                
                # If no native text, use OCR
                # Convert page to image
                pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x scale for better OCR
                img_data = pix.tobytes("png")
                
                # Extract text using OCR
                try:
                    ocr_text = self.extract_text_from_image(img_data)
                    if ocr_text:
                        texts.append(ocr_text)
                except Exception as e:
                    # Log OCR failure but continue with other pages
                    print(f"OCR failed for page {page_num + 1}: {e}")
                    continue
            
            return "\n\n".join(texts)
            
        except Exception as e:
            raise RuntimeError(f"PDF OCR failed: {str(e)}")
            
        finally:
            if doc:
                doc.close()
    
    def get_info(self) -> dict:
        """Get OCR service information"""
        return {
            'service': 'PaddleOCR v3.2.0',
            'model': 'PP-OCRv5 (Latest)',
            'status': self.status,
            'available': self.is_available(),
            'languages': ['English', 'Chinese'],
            'features': [
                'PP-OCRv5 Text Detection',
                'PP-OCRv5 Text Recognition',
                'Document Orientation Classification',
                'Multi-language Support',
                'CPU Optimized Processing'
            ],
            'processing': 'CPU-only (GPU not required)',
            'error': self._init_error
        }


# Backward compatibility alias
OcrService = PaddleOcrService
