#!/usr/bin/env python3
"""
Debug Chinese character encoding issues in archive file processing
"""

import os
import tempfile
import zipfile
from email_processor import EmailParser

def create_test_archive_with_chinese_filenames():
    """Create a test ZIP archive with Chinese filenames to test encoding"""

    # Create temporary file that persists
    temp_fd, zip_path = tempfile.mkstemp(suffix='.zip')
    os.close(temp_fd)  # Close the file descriptor

    try:
        # Create ZIP file with Chinese filenames
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Add files with Chinese names
            chinese_filenames = [
                "测试文档.txt",
                "报告2024年3月.pdf",
                "财务数据.xlsx",
                "会议记录-重要.docx"
            ]

            for filename in chinese_filenames:
                # Create some dummy content
                content = f"This is content for {filename}".encode('utf-8')
                zf.writestr(filename, content)

        return zip_path, chinese_filenames
    except Exception as e:
        # Clean up on error
        try:
            os.unlink(zip_path)
        except:
            pass
        raise e

def test_encoding_detection_patterns():
    """Test various encoding patterns that might occur"""
    
    parser = EmailParser()
    
    print("=== Testing Encoding Detection Patterns ===")
    
    # Test cases with different types of garbled Chinese
    test_cases = [
        # Original Chinese
        ("Original Chinese", "测试文档2024年3月26日.pdf"),
        
        # Common garbled patterns from different encoding issues
        ("CP437->UTF8 garbled", "测试文档2024年3月26日.pdf".encode('gbk').decode('cp437', errors='ignore')),
        ("Latin1->GBK garbled", "测试文档2024年3月26日.pdf".encode('gbk').decode('latin1', errors='ignore')),
        ("CP850->UTF8 garbled", "测试文档2024年3月26日.pdf".encode('gbk').decode('cp850', errors='ignore')),
        
        # Simulate the garbled text from the user's example
        ("User example garbled", "20240627╔╧╜╗╦∙╨¡▓Θ║»/╨¡▓Θí▓2024í│3261║┼.pdf"),
        
        # Mixed content
        ("Mixed content", "document_测试文档_2024.pdf"),
    ]
    
    results = []
    
    for name, test_filename in test_cases:
        print(f"\n--- Testing: {name} ---")
        print(f"Input: {repr(test_filename)}")
        
        try:
            # Test the encoding fix function
            fixed = parser._fix_filename_encoding(test_filename)
            print(f"Fixed: {repr(fixed)}")
            print(f"Display: {fixed}")
            
            # Analyze the result
            original_chinese = len([c for c in test_filename if '\u4e00' <= c <= '\u9fff'])
            fixed_chinese = len([c for c in fixed if '\u4e00' <= c <= '\u9fff'])
            
            original_garbled = len([c for c in test_filename if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚'])
            fixed_garbled = len([c for c in fixed if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚'])
            
            is_improved = (fixed_chinese > original_chinese) or (fixed_garbled < original_garbled)
            
            result = {
                'name': name,
                'original': test_filename,
                'fixed': fixed,
                'original_chinese': original_chinese,
                'fixed_chinese': fixed_chinese,
                'original_garbled': original_garbled,
                'fixed_garbled': fixed_garbled,
                'is_improved': is_improved
            }
            results.append(result)
            
            status = "✅ IMPROVED" if is_improved else "➡️ NO CHANGE"
            print(f"Status: {status}")
            print(f"Chinese chars: {original_chinese} -> {fixed_chinese}")
            print(f"Garbled chars: {original_garbled} -> {fixed_garbled}")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
            results.append({'name': name, 'error': str(e)})
    
    return results

def test_archive_processing_with_chinese():
    """Test actual archive processing with Chinese filenames"""
    
    print("\n=== Testing Archive Processing with Chinese Filenames ===")
    
    parser = EmailParser()
    
    try:
        # Create test archive
        zip_path, expected_filenames = create_test_archive_with_chinese_filenames()
        
        print(f"Created test archive: {zip_path}")
        print(f"Expected filenames: {expected_filenames}")
        
        # Read the archive file
        with open(zip_path, 'rb') as f:
            archive_data = f.read()
        
        # Process the archive
        result = parser.process_archive_attachment(archive_data, "test_chinese.zip")
        
        print(f"\nArchive processing result:")
        print(f"Status: {result['status']}")
        print(f"File count: {result['file_count']}")
        print(f"Error: {result['error']}")
        
        if result['file_list']:
            print(f"\nProcessed filenames:")
            for i, filename in enumerate(result['file_list'], 1):
                print(f"  {i}. {repr(filename)} -> {filename}")
                
                # Check if this looks like proper Chinese
                chinese_chars = len([c for c in filename if '\u4e00' <= c <= '\u9fff'])
                garbled_chars = len([c for c in filename if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚'])
                
                if chinese_chars > 0:
                    print(f"     ✅ Contains {chinese_chars} Chinese characters")
                elif garbled_chars > 0:
                    print(f"     ⚠️ Contains {garbled_chars} garbled characters")
                else:
                    print(f"     ➡️ ASCII/Latin characters")
        
        return result

    except Exception as e:
        print(f"❌ Archive processing test failed: {e}")
        return None
    finally:
        # Clean up the temporary file
        try:
            os.unlink(zip_path)
        except:
            pass

def test_manual_encoding_conversion():
    """Manually test encoding conversion for the user's specific example"""
    
    print("\n=== Manual Encoding Conversion Test ===")
    
    # The user's specific garbled filename
    garbled = "20240627╔╧╜╗╦∙╨¡▓Θ║»/╨¡▓Θí▓2024í│3261║┼.pdf"
    
    print(f"Original garbled: {garbled}")
    print(f"Repr: {repr(garbled)}")
    
    # Try various encoding combinations manually
    encoding_combinations = [
        ('cp437', 'gbk'),
        ('cp437', 'gb2312'),
        ('cp850', 'gbk'),
        ('cp850', 'gb2312'),
        ('latin1', 'gbk'),
        ('latin1', 'gb2312'),
        ('cp936', 'utf-8'),
        ('windows-1252', 'gbk'),
    ]
    
    print("\nTrying different encoding combinations:")
    
    for from_enc, to_enc in encoding_combinations:
        try:
            # Try to convert
            converted = garbled.encode(from_enc, errors='ignore').decode(to_enc, errors='ignore')
            
            # Check if it looks better
            chinese_chars = len([c for c in converted if '\u4e00' <= c <= '\u9fff'])
            garbled_chars = len([c for c in converted if c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚'])
            
            print(f"{from_enc:>12} -> {to_enc:<8}: {repr(converted)}")
            print(f"{'':>23} Display: {converted}")
            print(f"{'':>23} Chinese: {chinese_chars}, Garbled: {garbled_chars}")
            
            if chinese_chars > 0:
                print(f"{'':>23} ✅ SUCCESS - Found Chinese characters!")
            
            print()
            
        except Exception as e:
            print(f"{from_enc:>12} -> {to_enc:<8}: ❌ {e}")

if __name__ == "__main__":
    print("=== Debugging Chinese Character Encoding Issues ===")
    print()
    
    # Run all tests
    pattern_results = test_encoding_detection_patterns()
    archive_result = test_archive_processing_with_chinese()
    test_manual_encoding_conversion()
    
    # Summary
    print("\n" + "="*60)
    print("ENCODING DEBUG SUMMARY")
    print("="*60)
    
    improved_patterns = sum(1 for r in pattern_results if r.get('is_improved', False))
    print(f"Pattern detection: {improved_patterns}/{len(pattern_results)} cases improved")
    
    if archive_result:
        chinese_files = sum(1 for f in archive_result.get('file_list', []) if any('\u4e00' <= c <= '\u9fff' for c in f))
        total_files = len(archive_result.get('file_list', []))
        print(f"Archive processing: {chinese_files}/{total_files} files with Chinese characters")
    
    print("\nNext steps:")
    print("1. Improve encoding detection logic based on test results")
    print("2. Add better pattern recognition for garbled text")
    print("3. Test with real archive files from user's system")
