#!/usr/bin/env python3
"""
Simple test to verify GUI controls work correctly
"""

import sys
import os

def test_gui_controls():
    """Test that GUI controls are properly implemented"""
    print("=== Testing GUI Controls ===")
    
    try:
        # Import required modules
        from PyQt6.QtWidgets import QApplication
        from email_processor import EmailProcessorGUI
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ QApplication created")
        
        # Create GUI
        gui = EmailProcessorGUI()
        print("✅ EmailProcessorGUI created")
        
        # Test that new controls exist
        controls_to_check = [
            ('threading_enabled', 'Threading checkbox'),
            ('thread_count_spinbox', 'Thread count spinbox'),
            ('thread_count_label', 'Thread count label'),
            ('performance_label', 'Performance label'),
        ]
        
        for attr_name, description in controls_to_check:
            if hasattr(gui, attr_name):
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} missing")
                return False
        
        # Test initial values
        print(f"Threading enabled: {gui.threading_enabled.isChecked()}")
        print(f"Thread count: {gui.thread_count_spinbox.value()}")
        print(f"Thread count range: {gui.thread_count_spinbox.minimum()}-{gui.thread_count_spinbox.maximum()}")
        
        # Test toggle functionality
        print("\nTesting toggle functionality...")
        
        # Test disabling threading
        gui.threading_enabled.setChecked(False)
        gui.on_threading_toggled(False)
        print(f"Thread controls visible when disabled: {gui.thread_count_spinbox.isVisible()}")
        
        # Test enabling threading
        gui.threading_enabled.setChecked(True)
        gui.on_threading_toggled(True)
        print(f"Thread controls visible when enabled: {gui.thread_count_spinbox.isVisible()}")
        
        print("✅ GUI controls test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ GUI controls test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_thread_classes():
    """Test that thread classes can be imported and instantiated"""
    print("\n=== Testing Thread Classes ===")
    
    try:
        from email_processor import ProcessingThread, ParallelProcessingThread
        print("✅ Thread classes imported successfully")
        
        # Test ProcessingThread
        test_files = ["test1.eml", "test2.eml"]
        output_path = "test_output.xlsx"
        
        single_thread = ProcessingThread(test_files, output_path)
        print("✅ ProcessingThread created successfully")
        print(f"  Files: {len(single_thread.file_paths)}")
        print(f"  Output: {single_thread.output_path}")
        
        # Test ParallelProcessingThread
        parallel_thread = ParallelProcessingThread(test_files, output_path, max_workers=2)
        print("✅ ParallelProcessingThread created successfully")
        print(f"  Files: {len(parallel_thread.file_paths)}")
        print(f"  Output: {parallel_thread.output_path}")
        print(f"  Max workers: {parallel_thread.max_workers}")
        
        return True
        
    except Exception as e:
        print(f"❌ Thread classes test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_thread_safety():
    """Test OCR service thread safety features"""
    print("\n=== Testing OCR Thread Safety ===")
    
    try:
        from paddle_ocr_service import PaddleOcrService
        print("✅ PaddleOcrService imported successfully")
        
        # Create OCR service
        ocr_service = PaddleOcrService()
        print(f"OCR service status: {ocr_service.status}")
        print(f"OCR service available: {ocr_service.is_available()}")
        
        # Check thread-local attributes
        if hasattr(ocr_service, '_thread_local'):
            print("✅ Thread-local storage implemented")
        else:
            print("❌ Thread-local storage missing")
            return False
        
        if hasattr(ocr_service, '_initialization_lock'):
            print("✅ Initialization lock implemented")
        else:
            print("❌ Initialization lock missing")
            return False
        
        if hasattr(ocr_service, '_get_thread_local_ocr'):
            print("✅ Thread-local OCR method implemented")
        else:
            print("❌ Thread-local OCR method missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ OCR thread safety test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run simple tests"""
    print("Simple Parallel Processing Tests")
    print("=" * 40)
    
    results = []
    
    # Test GUI controls
    results.append(("GUI Controls", test_gui_controls()))
    
    # Test thread classes
    results.append(("Thread Classes", test_thread_classes()))
    
    # Test OCR thread safety
    results.append(("OCR Thread Safety", test_ocr_thread_safety()))
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST RESULTS SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All basic tests passed!")
        print("Parallel processing implementation is working correctly.")
    else:
        print("\n⚠️ Some tests failed - check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
