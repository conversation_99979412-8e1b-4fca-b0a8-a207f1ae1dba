#!/usr/bin/env python3
"""
Final verification test for the image resize fix
"""

import io
import sys
from PIL import Image
import numpy as np

def test_original_error_case():
    """Test the exact case mentioned in the original error"""
    print("=== Testing Original Error Case ===")
    print("Testing image with dimensions 5712x4284 (the exact case from the error message)")
    
    from paddle_ocr_service import PaddleOcrService
    
    try:
        # Initialize OCR service
        ocr_service = PaddleOcrService()
        
        # Create the exact image size that was causing the error
        print("Creating image with dimensions 5712x4284...")
        image = Image.new('RGB', (5712, 4284), color='white')
        
        # Add some simple content for OCR
        try:
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(image)
            
            # Add text that should be detectable
            positions = [
                (1000, 1000, "Test Document 1"),
                (3000, 2000, "Sample Text 2"),
                (4500, 3500, "OCR Test 3"),
            ]
            
            for x, y, text in positions:
                # Draw black rectangles to simulate text
                draw.rectangle([x, y, x+400, y+100], fill='black')
                draw.rectangle([x+10, y+10, x+390, y+90], fill='white')
                try:
                    font = ImageFont.load_default()
                    draw.text((x+20, y+30), text, fill='black', font=font)
                except:
                    pass
        except ImportError:
            # Fallback: create simple patterns
            pixels = image.load()
            for i in range(0, 5712, 500):
                for j in range(0, 4284, 500):
                    for x in range(i, min(i+100, 5712)):
                        for y in range(j, min(j+50, 4284)):
                            pixels[x, y] = (0, 0, 0)
        
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_bytes = img_byte_arr.getvalue()
        
        print(f"Original image: 5712x4284 pixels, {len(img_bytes)} bytes")
        print("Processing with improved OCR service...")
        
        # This should now work without crashing
        result = ocr_service.extract_text_from_image(img_bytes)
        
        print("✅ SUCCESS! The original error case now works correctly.")
        print(f"Extracted text length: {len(result)}")
        if result.strip():
            print(f"Sample extracted text: {result[:100]}...")
        else:
            print("No text detected (this is normal for synthetic test images)")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test various edge cases"""
    print("\n=== Testing Edge Cases ===")
    
    from paddle_ocr_service import PaddleOcrService
    ocr_service = PaddleOcrService()
    
    edge_cases = [
        {"name": "Exactly at limit", "size": (4000, 4000)},
        {"name": "Just over limit", "size": (4001, 3999)},
        {"name": "Very wide", "size": (10000, 500)},
        {"name": "Very tall", "size": (500, 10000)},
        {"name": "Square oversized", "size": (6000, 6000)},
        {"name": "Extreme aspect ratio", "size": (12000, 1000)},
    ]
    
    results = []
    
    for case in edge_cases:
        print(f"\n--- {case['name']}: {case['size'][0]}x{case['size'][1]} ---")
        
        try:
            width, height = case['size']
            
            # Create simple test image
            image = Image.new('RGB', (width, height), color='white')
            
            # Add minimal content
            try:
                from PIL import ImageDraw
                draw = ImageDraw.Draw(image)
                draw.rectangle([width//4, height//4, 3*width//4, 3*height//4], outline='black', width=5)
                draw.text((width//2-50, height//2-10), "TEST", fill='black')
            except:
                pass
            
            # Convert to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()
            
            # Process with OCR
            result = ocr_service.extract_text_from_image(img_bytes)
            
            print(f"✅ SUCCESS - Text length: {len(result)}")
            results.append({"case": case['name'], "status": "SUCCESS"})
            
        except Exception as e:
            print(f"❌ FAILED - Error: {e}")
            results.append({"case": case['name'], "status": "FAILED", "error": str(e)})
    
    # Summary
    successful = sum(1 for r in results if r['status'] == 'SUCCESS')
    total = len(results)
    print(f"\nEdge case results: {successful}/{total} successful")
    
    return successful == total

def test_real_world_scenario():
    """Test a scenario that simulates real-world usage"""
    print("\n=== Testing Real-World Scenario ===")
    
    from paddle_ocr_service import PaddleOcrService
    
    try:
        ocr_service = PaddleOcrService()
        
        # Simulate processing multiple images of different sizes
        # (like what might happen in email processing)
        test_images = [
            (2000, 1500),   # Normal document scan
            (3000, 4000),   # Portrait document
            (5000, 3000),   # High-res landscape
            (4500, 6000),   # High-res portrait
            (8000, 2000),   # Wide banner/receipt
        ]
        
        print("Simulating batch processing of multiple images...")
        
        for i, (width, height) in enumerate(test_images, 1):
            print(f"Processing image {i}/5: {width}x{height}")
            
            # Create test image
            image = Image.new('RGB', (width, height), color='white')
            
            # Add some content
            try:
                from PIL import ImageDraw
                draw = ImageDraw.Draw(image)
                draw.text((100, 100), f"Document {i}", fill='black')
                draw.rectangle([50, 50, width-50, height-50], outline='black', width=2)
            except:
                pass
            
            # Convert and process
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()
            
            result = ocr_service.extract_text_from_image(img_bytes)
            print(f"  ✅ Processed successfully, text length: {len(result)}")
            
            # Clean up
            del image, img_byte_arr, img_bytes
        
        print("✅ Real-world scenario test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Real-world scenario failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Final Verification of Image Resize Fix ===")
    print("This test verifies that the original error has been resolved")
    print()
    
    # Test results
    test1_passed = test_original_error_case()
    test2_passed = test_edge_cases()
    test3_passed = test_real_world_scenario()
    
    print("\n" + "="*50)
    print("FINAL RESULTS:")
    print("="*50)
    
    print(f"✅ Original error case (5712x4284): {'PASSED' if test1_passed else 'FAILED'}")
    print(f"✅ Edge cases: {'PASSED' if test2_passed else 'FAILED'}")
    print(f"✅ Real-world scenario: {'PASSED' if test3_passed else 'FAILED'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 ALL TESTS PASSED! The image resize issue has been successfully fixed.")
        print("\nThe system now:")
        print("- Automatically detects oversized images")
        print("- Resizes them to fit within the 4000-pixel limit")
        print("- Preserves aspect ratio during resizing")
        print("- Continues processing without crashing")
        print("- Provides clear logging about the resize operations")
    else:
        print("\n❌ Some tests failed. Please review the errors above.")
    
    print("\n" + "="*50)
