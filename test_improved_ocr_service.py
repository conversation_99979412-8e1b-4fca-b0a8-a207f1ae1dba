#!/usr/bin/env python3
"""
Test the improved OCR service with robust image preprocessing
"""

import io
import sys
from PIL import Image
import numpy as np

def create_test_image_with_text(width=5712, height=4284, text_content="Sample Text"):
    """Create a test image with actual text content"""
    print(f"Creating test image with dimensions: {width}x{height}")
    
    # Create a white background image
    image = Image.new('RGB', (width, height), color='white')
    
    try:
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(image)
        
        # Try to use a default font, fallback to basic if not available
        try:
            # Try to load a larger font for better OCR
            font_size = min(width, height) // 20  # Scale font with image size
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.load_default()
            except:
                font = None
        
        # Add text at various positions
        text_positions = [
            (width // 4, height // 4),
            (width // 2, height // 2),
            (3 * width // 4, 3 * height // 4),
        ]
        
        for i, (x, y) in enumerate(text_positions):
            text = f"{text_content} {i+1}"
            if font:
                draw.text((x, y), text, fill='black', font=font)
            else:
                # Fallback: draw simple rectangles to simulate text
                draw.rectangle([x, y, x+200, y+50], fill='black')
                draw.rectangle([x+10, y+10, x+190, y+40], fill='white')
    
    except ImportError:
        # If PIL drawing is not available, create simple patterns
        pixels = image.load()
        for i in range(0, width, 200):
            for j in range(0, height, 200):
                # Add some rectangles to simulate text
                for x in range(i, min(i+100, width)):
                    for y in range(j, min(j+30, height)):
                        if x < width and y < height:
                            pixels[x, y] = (0, 0, 0)  # Black text
    
    return image

def test_improved_ocr_service():
    """Test the improved OCR service with various image sizes"""
    print("=== Testing Improved OCR Service ===")
    
    from paddle_ocr_service import PaddleOcrService
    
    # Test cases with different image sizes
    test_cases = [
        {"name": "Small image", "size": (800, 600), "text": "Small Test"},
        {"name": "Medium image", "size": (2000, 1500), "text": "Medium Test"},
        {"name": "Large image (at limit)", "size": (4000, 3000), "text": "Large Test"},
        {"name": "Oversized width", "size": (6000, 2000), "text": "Wide Test"},
        {"name": "Oversized height", "size": (2000, 6000), "text": "Tall Test"},
        {"name": "Both dimensions oversized", "size": (5712, 4284), "text": "Huge Test"},
        {"name": "Very large image", "size": (8000, 6000), "text": "Very Large Test"},
    ]
    
    ocr_service = PaddleOcrService()
    
    results = []
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']}: {test_case['size'][0]}x{test_case['size'][1]} ---")
        
        try:
            # Create test image
            width, height = test_case['size']
            image = create_test_image_with_text(width, height, test_case['text'])
            
            # Convert to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()
            
            print(f"Image created: {len(img_bytes)} bytes")
            
            # Run OCR
            result_text = ocr_service.extract_text_from_image(img_bytes)
            
            result = {
                'name': test_case['name'],
                'size': test_case['size'],
                'status': 'SUCCESS',
                'text_length': len(result_text),
                'extracted_text': result_text[:100] + '...' if len(result_text) > 100 else result_text
            }
            
            print(f"✅ SUCCESS - Extracted text length: {len(result_text)}")
            if result_text.strip():
                print(f"Sample text: {result_text[:50]}...")
            
        except Exception as e:
            result = {
                'name': test_case['name'],
                'size': test_case['size'],
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ FAILED - Error: {e}")
        
        results.append(result)
    
    # Print summary
    print("\n=== Test Summary ===")
    successful = sum(1 for r in results if r['status'] == 'SUCCESS')
    total = len(results)
    
    print(f"Successful: {successful}/{total}")
    print(f"Failed: {total - successful}/{total}")
    
    for result in results:
        status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
        print(f"{status_icon} {result['name']}: {result['size'][0]}x{result['size'][1]} - {result['status']}")
        if result['status'] == 'FAILED':
            print(f"   Error: {result['error']}")
    
    return results

def test_memory_efficiency():
    """Test memory efficiency with multiple large images"""
    print("\n=== Testing Memory Efficiency ===")
    
    from paddle_ocr_service import PaddleOcrService
    ocr_service = PaddleOcrService()
    
    # Process multiple large images to test memory handling
    for i in range(3):
        print(f"\n--- Processing large image {i+1}/3 ---")
        try:
            # Create a large image
            image = create_test_image_with_text(6000, 4500, f"Memory Test {i+1}")
            
            # Convert to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()
            
            print(f"Processing image {i+1}: {len(img_bytes)} bytes")
            
            # Run OCR
            result = ocr_service.extract_text_from_image(img_bytes)
            
            print(f"✅ Image {i+1} processed successfully, text length: {len(result)}")
            
            # Clean up to free memory
            del image, img_byte_arr, img_bytes
            import gc
            gc.collect()
            
        except Exception as e:
            print(f"❌ Image {i+1} failed: {e}")

if __name__ == "__main__":
    print("=== Testing Improved PaddleOCR Service ===")
    print("This script tests the enhanced OCR service with robust image preprocessing")
    print()
    
    # Test the improved service
    test_improved_ocr_service()
    
    # Test memory efficiency
    test_memory_efficiency()
    
    print("\n=== Testing Complete ===")
