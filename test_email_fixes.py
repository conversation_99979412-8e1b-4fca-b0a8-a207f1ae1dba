#!/usr/bin/env python3
"""
Test script to verify the fixes for the three critical email processing issues:
1. Excel file corruption
2. HTML/CSS content cleaning
3. Chinese character encoding in archives
"""

import os
import tempfile
import zipfile
from email_processor import EmailParser
import openpyxl

def test_excel_sanitization():
    """Test the improved Excel value sanitization"""
    print("=== Testing Excel Value Sanitization ===")
    
    parser = EmailParser()
    
    # Test cases with problematic characters
    test_cases = [
        # XML-invalid characters
        ("Control chars", "Hello\x00\x01\x02World\x0B\x0C"),
        # Unicode issues
        ("Unicode mixed", "测试\u0000文本\x1F数据"),
        # Formula injection
        ("Formula injection", "=SUM(A1:A10)"),
        # Long content
        ("Long content", "A" * 40000),
        # CSS-like content
        ("CSS content", "body { line-height: 1.5; }blockquote { margin: 0px; }"),
        # VML content
        ("VML content", "v\\:* {behavior:url(#default#VML);} o\\:* {behavior:url(#default#VML);}"),
        # Chinese characters with encoding issues
        ("Chinese garbled", "20240627╔╧╜╗╦∙╨¡▓Θ║»/╨¡▓Θí▓2024í│3261║┼"),
        # Mixed problematic content
        ("Mixed issues", "=FORMULA\x00测试\x0B{ font-family: Arial; }"),
    ]
    
    results = []
    for name, test_value in test_cases:
        try:
            sanitized = parser.sanitize_excel_value(test_value)
            
            # Check if sanitized value is safe
            is_safe = True
            issues = []
            
            # Check for XML-invalid characters
            for char in sanitized:
                code = ord(char)
                if not (code == 0x09 or code == 0x0A or code == 0x0D or 
                       (0x20 <= code <= 0xD7FF) or (0xE000 <= code <= 0xFFFD) or 
                       (0x10000 <= code <= 0x10FFFF)):
                    is_safe = False
                    issues.append(f"XML-invalid char: {repr(char)}")
                    break
            
            # Check for formula injection
            if sanitized and sanitized[0] in ['=', '+', '-', '@'] and not sanitized.startswith("'"):
                is_safe = False
                issues.append("Formula injection not prevented")
            
            # Check length
            if len(sanitized) > 32767:
                is_safe = False
                issues.append("Length exceeds Excel limit")
            
            result = {
                'name': name,
                'original_length': len(test_value),
                'sanitized_length': len(sanitized),
                'is_safe': is_safe,
                'issues': issues,
                'sample': sanitized[:50] + '...' if len(sanitized) > 50 else sanitized
            }
            results.append(result)
            
            status = "✅ SAFE" if is_safe else "❌ UNSAFE"
            print(f"{status} {name}: {len(test_value)} -> {len(sanitized)} chars")
            if issues:
                print(f"  Issues: {', '.join(issues)}")
            
        except Exception as e:
            print(f"❌ ERROR {name}: {e}")
            results.append({'name': name, 'error': str(e)})
    
    return results

def test_html_css_cleaning():
    """Test the improved HTML/CSS content cleaning"""
    print("\n=== Testing HTML/CSS Content Cleaning ===")
    
    parser = EmailParser()
    
    # Test cases with problematic HTML/CSS content
    test_cases = [
        ("CSS styles", """body { line-height: 1.5; }blockquote { margin-top: 0px; margin-bottom: 0px; margin-left: 0.5em; }body { font-size: 14px; font-family: "Microsoft YaHei UI"; color: rgb(0, 0, 0); line-height: 1.5; }"""),
        ("VML declarations", """v\\:* {behavior:url(#default#VML);} o\\:* {behavior:url(#default#VML);} x\\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);}"""),
        ("Mixed HTML/CSS", """<div class="FoxDiv20250421155517288198">body { font-size: 14px; font-family: "Microsoft YaHei UI"; color: rgb(0, 0, 0); line-height: 1.5; }<p style="margin: 0;">Hello World</p></div>"""),
        ("Style blocks", """<style>body { background: white; } .test { color: red; }</style><p>Content here</p>"""),
        ("Inline styles", """<div style="color: red; font-size: 14px;">Test content</div>"""),
        ("Complex HTML", """<html><head><style>body{margin:0}</style></head><body><div class="content" style="padding:10px"><p>Real content</p></div></body></html>"""),
    ]
    
    results = []
    for name, test_content in test_cases:
        try:
            cleaned = parser._clean_html_content(test_content)
            
            # Check if CSS/HTML artifacts are removed
            has_css = any(pattern in cleaned.lower() for pattern in [
                'font-family:', 'color:', 'margin:', 'padding:', 'line-height:',
                'behavior:url', '{', '}', '<', '>'
            ])
            
            result = {
                'name': name,
                'original_length': len(test_content),
                'cleaned_length': len(cleaned),
                'has_css_artifacts': has_css,
                'sample': cleaned[:100] + '...' if len(cleaned) > 100 else cleaned
            }
            results.append(result)
            
            status = "✅ CLEAN" if not has_css else "❌ HAS CSS"
            print(f"{status} {name}: {len(test_content)} -> {len(cleaned)} chars")
            if has_css:
                print(f"  Sample: {cleaned[:50]}...")
            
        except Exception as e:
            print(f"❌ ERROR {name}: {e}")
            results.append({'name': name, 'error': str(e)})
    
    return results

def test_chinese_encoding_fix():
    """Test the Chinese character encoding fix"""
    print("\n=== Testing Chinese Character Encoding Fix ===")
    
    parser = EmailParser()
    
    # Test cases with garbled Chinese characters
    test_cases = [
        ("Garbled Chinese 1", "20240627╔╧╜╗╦∙╨¡▓Θ║»/╨¡▓Θí▓2024í│3261║┼-┴¬┤ó╓ñ╚»-603586-(20240627).pdf"),
        ("Garbled Chinese 2", "╨¡▓Θ║»╓ñ╚»2024╞┌3╘┬26╚╒.docx"),
        ("Mixed encoding", "测试文档-╔╧╜╗╦∙╨¡▓Θ.txt"),
        ("Normal Chinese", "测试文档2024年3月26日.pdf"),
        ("English filename", "document_2024_03_26.txt"),
    ]
    
    results = []
    for name, test_filename in test_cases:
        try:
            fixed = parser._fix_filename_encoding(test_filename)
            
            # Check if the result looks more reasonable
            # Count control characters and special symbols
            original_bad_chars = len([c for c in test_filename if ord(c) < 32 or c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚'])
            fixed_bad_chars = len([c for c in fixed if ord(c) < 32 or c in '╔╧╜╗╦∙╨¡▓Θ║»í│┼┴¬┤ó╓ñ╚'])
            
            is_improved = fixed_bad_chars < original_bad_chars or (fixed != test_filename and '测试' in fixed)
            
            result = {
                'name': name,
                'original': test_filename,
                'fixed': fixed,
                'is_improved': is_improved,
                'original_bad_chars': original_bad_chars,
                'fixed_bad_chars': fixed_bad_chars
            }
            results.append(result)
            
            status = "✅ IMPROVED" if is_improved else "➡️ UNCHANGED"
            print(f"{status} {name}:")
            print(f"  Original: {test_filename}")
            print(f"  Fixed:    {fixed}")
            
        except Exception as e:
            print(f"❌ ERROR {name}: {e}")
            results.append({'name': name, 'error': str(e)})
    
    return results

def test_excel_file_creation():
    """Test creating an Excel file with the fixes to ensure no corruption"""
    print("\n=== Testing Excel File Creation ===")
    
    try:
        parser = EmailParser()
        
        # Create test data with problematic content
        test_data = [
            {
                'subject': 'Test Email with Issues',
                'body_content': 'body { line-height: 1.5; }<div>Hello\x00World</div>',
                'archive_file_list': '20240627╔╧╜╗╦∙╨¡▓Θ║»/╨¡▓Θí▓2024í│3261║┼.pdf',
                'sender_email': '<EMAIL>'
            }
        ]
        
        # Create temporary Excel file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Email Analysis"
            
            # Write headers
            headers = ['Subject', 'Body Content', 'Archive File List', 'Sender Email']
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)
            
            # Write test data with sanitization
            for row, data in enumerate(test_data, 2):
                values = [
                    parser.sanitize_excel_value(data['subject']),
                    parser.sanitize_excel_value(data['body_content']),
                    parser.sanitize_excel_value(data['archive_file_list']),
                    parser.sanitize_excel_value(data['sender_email'])
                ]
                
                for col, value in enumerate(values, 1):
                    ws.cell(row=row, column=col, value=value)
            
            # Save the file
            wb.save(temp_path)
            
            # Try to open the file to verify it's not corrupted
            wb_test = openpyxl.load_workbook(temp_path)
            ws_test = wb_test.active
            
            # Read back the data
            read_data = []
            for row in range(2, len(test_data) + 2):
                row_data = []
                for col in range(1, len(headers) + 1):
                    cell_value = ws_test.cell(row=row, column=col).value
                    row_data.append(cell_value)
                read_data.append(row_data)
            
            print("✅ Excel file created and read successfully!")
            print(f"File path: {temp_path}")
            print("Sample data read back:")
            for i, row_data in enumerate(read_data):
                print(f"  Row {i+1}: {[str(val)[:50] + '...' if len(str(val)) > 50 else str(val) for val in row_data]}")
            
            return True, temp_path
            
        except Exception as e:
            print(f"❌ Excel file creation failed: {e}")
            return False, None
            
        finally:
            # Clean up
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False, None

if __name__ == "__main__":
    print("=== Testing Email Processing Fixes ===")
    print("This script tests the fixes for Excel corruption, HTML/CSS cleaning, and Chinese encoding issues")
    print()
    
    # Run all tests
    excel_results = test_excel_sanitization()
    html_results = test_html_css_cleaning()
    encoding_results = test_chinese_encoding_fix()
    excel_file_success, excel_file_path = test_excel_file_creation()
    
    # Summary
    print("\n" + "="*60)
    print("SUMMARY OF FIXES")
    print("="*60)
    
    excel_safe = sum(1 for r in excel_results if r.get('is_safe', False))
    html_clean = sum(1 for r in html_results if not r.get('has_css_artifacts', True))
    encoding_improved = sum(1 for r in encoding_results if r.get('is_improved', False))
    
    print(f"✅ Excel sanitization: {excel_safe}/{len(excel_results)} test cases safe")
    print(f"✅ HTML/CSS cleaning: {html_clean}/{len(html_results)} test cases clean")
    print(f"✅ Chinese encoding: {encoding_improved}/{len(encoding_results)} test cases improved")
    print(f"✅ Excel file creation: {'SUCCESS' if excel_file_success else 'FAILED'}")
    
    if all([excel_safe == len(excel_results), 
            html_clean == len(html_results), 
            excel_file_success]):
        print("\n🎉 ALL FIXES WORKING CORRECTLY!")
    else:
        print("\n⚠️ Some issues may remain - please review the test results above.")
    
    print("\n" + "="*60)
